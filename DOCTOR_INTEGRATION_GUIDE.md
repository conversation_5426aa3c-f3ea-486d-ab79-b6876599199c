# External Platform Doctor Integration Guide

## Overview

External platforms can now integrate with the `expo-doctor` command to provide comprehensive health checking and prerequisite validation. This ensures developers get clear guidance when setting up external platform development environments.

## How It Works

When you run `npx expo-doctor`, it automatically:

1. **Detects External Platforms**: Reads `expo.platforms` from your app.json/app.config.js
2. **Loads Platform Packages**: Dynamically loads installed `expo-platform-*` packages
3. **Runs Health Checks**: Executes platform-specific prerequisite validation
4. **Reports Results**: Provides detailed error messages and installation guidance

## For External Platform Developers

### 1. Implement ExternalPlatformPrerequisite

Create a prerequisite class that extends the base class:

```typescript
// src/prerequisites/YourPlatformPrerequisite.ts
import { ExternalPlatformPrerequisite } from '@expo/cli/src/core/PlatformRegistry';

export class YourPlatformPrerequisite extends ExternalPlatformPrerequisite {
  constructor(platform: string) {
    super(platform);
  }

  async checkDevelopmentEnvironment(): Promise<boolean> {
    // Check if development environment is set up
    // e.g., check Node.js version, OS compatibility
    return true;
  }

  async checkSystemRequirements(): Promise<boolean> {
    // Check system-level requirements
    // e.g., OS version, available memory, disk space
    return true;
  }

  async checkPlatformTools(): Promise<boolean> {
    // Check platform-specific tools
    // e.g., SDKs, build tools, emulators
    return true;
  }

  getInstallationInstructions(): string[] {
    return [
      'Install YourPlatform SDK from https://yourplatform.dev/sdk',
      'Install YourPlatform CLI: npm install -g yourplatform-cli',
      'Configure development environment: yourplatform setup',
    ];
  }
}
```

### 2. Register with Your Platform

Add the prerequisite constructor to your platform registration:

```typescript
// src/index.ts
import { ExternalPlatform } from '@expo/cli/src/core/PlatformRegistry';
import { YourPlatformPrerequisite } from './prerequisites/YourPlatformPrerequisite';

export const yourPlatform: ExternalPlatform = {
  platform: 'yourplatform',
  displayName: 'Your Platform',
  
  // Add prerequisite constructor
  prerequisiteConstructor: YourPlatformPrerequisite,
  
  // ... other platform properties
};
```

### 3. Export the Prerequisite Class

Make sure to export your prerequisite class:

```typescript
// src/index.ts
export { YourPlatformPrerequisite } from './prerequisites/YourPlatformPrerequisite';
```

## For App Developers

### Usage

Simply run the doctor command to check all configured platforms:

```bash
npx expo-doctor
```

### Example Output

```
✔ Check package.json for common issues
✔ Check Expo config for common issues  
✔ External platform prerequisites and health
✔ Check native tooling versions
...

Didn't find any issues with the project!
```

### When Issues Are Found

```
✖ External platform prerequisites and health

Detailed check results:

windows: Platform "windows" prerequisites not met:
  ✗ Development Environment
  ✗ System Requirements  
  ✗ Platform Tools

To fix these issues:
  • Install Visual Studio 2019 or 2022 with the following workloads:
    • Desktop development with C++
    • Universal Windows Platform development
  • Install Windows 10/11 SDK (latest version)
  • For react-native-windows setup, run: npx @react-native-windows/cli@latest init

Advice: Follow the platform-specific installation instructions shown above to resolve prerequisite issues
```

## Configuration

### Platform Detection

The doctor command automatically detects external platforms from your Expo configuration:

```json
{
  "expo": {
    "name": "MyApp",
    "platforms": ["ios", "android", "windows", "macos"]
  }
}
```

### Disabling Checks

If you want to skip external platform checks, you can remove platforms from the `platforms` array or uninstall the platform packages.

## Best Practices

### For Platform Developers

1. **Comprehensive Checks**: Validate all critical requirements (OS, tools, SDKs)
2. **Clear Messages**: Provide specific error messages explaining what's missing
3. **Actionable Instructions**: Give step-by-step installation guidance
4. **Graceful Degradation**: Handle missing tools gracefully without crashing
5. **Performance**: Keep checks fast and efficient

### Example Implementation Patterns

```typescript
// Check for required tools
async checkPlatformTools(): Promise<boolean> {
  try {
    // Check if CLI tool is available
    execSync('yourplatform-cli --version', { stdio: 'pipe' });
    
    // Check if SDK is installed
    const sdkPath = process.env.YOURPLATFORM_SDK_ROOT;
    if (!sdkPath || !fs.existsSync(sdkPath)) {
      return false;
    }
    
    return true;
  } catch (error) {
    return false;
  }
}

// Provide helpful installation instructions
getInstallationInstructions(): string[] {
  const instructions = [];
  
  if (process.platform === 'win32') {
    instructions.push('Download YourPlatform SDK for Windows from: https://...');
  } else if (process.platform === 'darwin') {
    instructions.push('Install via Homebrew: brew install yourplatform-sdk');
  } else {
    instructions.push('Install via package manager or download from: https://...');
  }
  
  instructions.push('Set YOURPLATFORM_SDK_ROOT environment variable');
  instructions.push('Run: yourplatform doctor to verify installation');
  
  return instructions;
}
```

## Integration Benefits

- **Consistent Experience**: Same health checking workflow as built-in platforms
- **Early Problem Detection**: Catch setup issues before development starts
- **Guided Resolution**: Clear instructions for fixing problems
- **Automated Validation**: No manual environment checking needed
- **CI/CD Integration**: Can be used in automated workflows

## Troubleshooting

### Common Issues

1. **Platform Not Detected**: Ensure platform is listed in `expo.platforms`
2. **Package Not Found**: Verify `expo-platform-*` package is installed
3. **Import Errors**: Check that prerequisite class is properly exported
4. **Check Failures**: Review platform-specific installation requirements

### Debug Mode

Set `EXPO_DEBUG=1` to see detailed information about platform loading and checking:

```bash
EXPO_DEBUG=1 npx expo-doctor
```
