# Platform Registry Design Proposal

## Problem Statement

The current Platform Registry location in `@expo/cli` creates circular dependency issues and inconsistent import patterns across the Expo ecosystem. Multiple packages need to access platform registry functionality but must use different workarounds to avoid circular dependencies.

## Proposed Solution: @expo/platform-registry Package

### Package Structure

```
packages/@expo/platform-registry/
├── src/
│   ├── types/
│   │   ├── ExternalPlatform.ts      # Core platform interface
│   │   ├── DeviceManager.ts         # Device management types
│   │   ├── PlatformManager.ts       # Platform manager types
│   │   ├── Prerequisites.ts         # Health check types
│   │   ├── Dependencies.ts          # Dependency resolution types
│   │   └── index.ts                 # Type exports
│   ├── registry/
│   │   ├── PlatformRegistry.ts      # Core registry implementation
│   │   ├── PlatformDiscovery.ts     # Platform discovery logic
│   │   ├── ErrorMessages.ts         # Standardized error messages
│   │   └── index.ts                 # Registry exports
│   ├── utils/
│   │   ├── validation.ts            # Platform validation utilities
│   │   ├── loading.ts               # Platform loading utilities
│   │   └── index.ts                 # Utility exports
│   └── index.ts                     # Main package exports
├── package.json
├── README.md
└── CHANGELOG.md
```

### Benefits

1. **Eliminates Circular Dependencies**
   - Platform registry becomes a leaf dependency
   - All packages can import directly without workarounds
   - Consistent import patterns across ecosystem

2. **Follows Expo Team Patterns**
   - Dedicated package for shared functionality
   - Clean separation of concerns
   - Consistent with @expo/config-types pattern

3. **Improved Developer Experience**
   - Single source of truth for platform types
   - Clear API surface for external platforms
   - Better documentation and examples

4. **Enhanced Testing**
   - Isolated testing without CLI dependencies
   - Mock registry for testing external platforms
   - Better unit test coverage

### Migration Strategy

#### Phase 1: Create New Package
1. Create `@expo/platform-registry` package
2. Move types from CLI to new package
3. Implement registry with same API
4. Add comprehensive tests

#### Phase 2: Update Core Packages
1. Update `@expo/cli` to use new package
2. Update `@expo/metro-config` imports
3. Update `@expo/prebuild-config` imports
4. Update `expo-doctor` imports

#### Phase 3: Update External Platforms
1. Update `expo-platform-*` packages
2. Update documentation and guides
3. Deprecate old CLI exports
4. Provide migration guide

#### Phase 4: Cleanup
1. Remove old platform registry from CLI
2. Update all references in documentation
3. Release major version with breaking changes

### API Design

#### Clean Type Exports
```typescript
// @expo/platform-registry/types
export interface ExternalPlatform {
  platform: string;
  displayName?: string;
  // ... other properties
}

export interface ExternalPlatformDeviceManagerConstructor<TDevice> {
  new (device: TDevice): DeviceManager<TDevice>;
  resolveAsync(options?: BaseResolveDeviceProps<TDevice>): Promise<DeviceManager<TDevice>>;
}

// ... other type exports
```

#### Registry Implementation
```typescript
// @expo/platform-registry/registry
export class PlatformRegistry {
  private platforms = new Map<string, ExternalPlatform>();
  
  register(platformData: ExternalPlatform): void {
    // Implementation
  }
  
  // ... other methods
}

export const platformRegistry = new PlatformRegistry();
```

#### Discovery Service
```typescript
// @expo/platform-registry/registry
export class PlatformDiscovery {
  static async loadExternalPlatforms(projectRoot?: string): Promise<void> {
    // Implementation
  }
  
  // ... other methods
}
```

### Package Dependencies

```json
{
  "name": "@expo/platform-registry",
  "dependencies": {
    "@expo/config-types": "^52.0.5",
    "debug": "^4.3.4",
    "fs-extra": "^11.1.1"
  },
  "devDependencies": {
    "@types/debug": "^4.1.5",
    "@types/fs-extra": "^11.0.1",
    "expo-module-scripts": "^4.0.5"
  }
}
```

### Updated Import Patterns

#### Before (with workarounds):
```typescript
// Metro config - dynamic import with fallbacks
let platformRegistryModule;
try {
  platformRegistryModule = require('@expo/cli/build/src/core/PlatformRegistry');
} catch {
  platformRegistryModule = require('@expo/cli/src/core/PlatformRegistry');
}

// Prebuild config - lazy loading
let withExternalPlatformPlugins: any = null;
function loadExternalPlatformIntegration() {
  if (!withExternalPlatformPlugins) {
    try {
      const module = require('@expo/cli/build/src/prebuild/withExternalPlatformPlugins');
      withExternalPlatformPlugins = module.withExternalPlatformPlugins;
    } catch {
      withExternalPlatformPlugins = (config: any) => config;
    }
  }
}
```

#### After (clean imports):
```typescript
// All packages - consistent imports
import { platformRegistry, PlatformDiscovery } from '@expo/platform-registry';
import type { ExternalPlatform } from '@expo/platform-registry/types';

// External platforms - clean API
import { ExternalPlatform, platformRegistry } from '@expo/platform-registry';

export const windowsPlatform: ExternalPlatform = {
  platform: 'windows',
  displayName: 'Windows',
  // ... implementation
};

platformRegistry.register(windowsPlatform);
```

### Compatibility

#### Backward Compatibility
- CLI package re-exports from new package during transition
- Deprecation warnings for old imports
- Migration guide for external platforms

#### Forward Compatibility
- Extensible type system for future platform features
- Versioned API for breaking changes
- Plugin system for advanced integrations

## Conclusion

Creating a dedicated `@expo/platform-registry` package aligns with Expo team patterns, eliminates circular dependencies, and provides a clean foundation for the external platform system. This approach follows the single responsibility principle and creates a stable API surface for external platform developers.
